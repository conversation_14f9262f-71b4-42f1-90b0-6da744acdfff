```json
[
  {
    "scenario_name": "Successful Login After Account Creation",
    "type": "positive",
    "prerequisites": "User should have created an account and possess valid credentials for the test environment.",
    "Test Case Objective": "Verify successful login with correct credentials after account creation.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the username field", "expected_result": "Username should be successfully entered into the username field."},
      {"action": "Verify if user is able to enter valid password in the password field", "expected_result": "Password should be successfully entered into the password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "User should be able to successfully click the Login button."},
      {"action": "Verify if user is able to access the user dashboard", "expected_result": "User dashboard should be displayed upon successful login."}
    ]
  },
  {
    "scenario_name": "Password Reset Request",
    "type": "positive",
    "prerequisites": "User should have a registered account but have forgotten their password.",
    "Test Case Objective": "Verify the password reset request process with a registered email address.",
    "steps": [
      {"action": "Verify if user is able to click on the 'Forgot Password' link", "expected_result": "Password reset page should be displayed."},
      {"action": "Verify if user is able to enter registered email address in the 'Email' field", "expected_result": "Email address should be successfully entered into the 'Email' field."},
      {"action": "Verify if user is able to click on the 'Reset Password' button", "expected_result": "A password reset link should be sent to the user's registered email address."},
      {"action": "Verify if user is able to receive the password reset email in their inbox", "expected_result": "The password reset email should be present in the user's inbox."}
    ]
  },
  {
    "scenario_name": "Password Resetting Using Link",
    "type": "positive",
    "prerequisites": "User should have received a password reset email.",
    "Test Case Objective": "Verify the user's ability to reset their password using the link received in the password reset email.",
    "steps": [
      {"action": "Verify if user is able to click the password reset link in the email", "expected_result": "The password reset page should be displayed."},
      {"action": "Verify if user is able to enter a new password in the 'New Password' field", "expected_result": "The new password should be successfully entered in the 'New Password' field."},
      {"action": "Verify if user is able to confirm the new password in the 'Confirm Password' field", "expected_result": "The confirmed password should match the new password."},
      {"action": "Verify if user is able to click the 'Submit' button", "expected_result": "The password should be reset successfully."}
    ]
  },
  {
    "scenario_name": "Successful Login After Password Reset",
    "type": "positive",
    "prerequisites": "User should have successfully reset their password.",
    "Test Case Objective": "Verify successful login using the newly reset password.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the username field", "expected_result": "Username should be successfully entered into the username field."},
      {"action": "Verify if user is able to enter the new password in the password field", "expected_result": "New password should be successfully entered into the password field."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "User should be able to successfully click the Login button."},
      {"action": "Verify if user is able to access the user dashboard", "expected_result": "User dashboard should be displayed upon successful login."}
    ]
  },
  {
    "scenario_name": "Account Lockout After Three Attempts",
    "type": "positive",
    "prerequisites": "User should have a valid account.",
    "Test Case Objective": "Verify the system locks out the user after three incorrect login attempts and prompts a message.",
    "steps": [
      {"action": "Verify if user is able to enter incorrect username and password", "expected_result": "System should display an 'invalid credentials' message."},
      {"action": "Verify if user is able to enter incorrect username and password a second time", "expected_result": "System should display an 'invalid credentials' message."},
      {"action": "Verify if user is able to enter incorrect username and password a third time", "expected_result": "The system should lock the user's account and prompt an appropriate lockout message."}
    ]
  }
]
```