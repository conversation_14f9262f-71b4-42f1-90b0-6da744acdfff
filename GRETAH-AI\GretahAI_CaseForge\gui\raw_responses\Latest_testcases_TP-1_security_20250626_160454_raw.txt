```json
[
  {
    "scenario_name": "TC_031: Verify Password Complexity Requirements",
    "type": "security",
    "prerequisites": "User should have access to the login page and a valid, but easily guessable, password.",
    "Test Case Objective": "Verify that the system enforces password complexity requirements and rejects weak passwords.",
    "steps": [
      {"action": "Verify if user is able to enter a weak password (e.g., 'password', '123456') into the password field", "expected_result": "The system should display an error message indicating that the password does not meet complexity requirements."},
      {"action": "Verify if user is able to enter a password that is too short (e.g., less than 8 characters)", "expected_result": "The system should display an error message indicating that the password must be at least 8 characters long."},
      {"action": "Verify if user is able to login with a weak password after attempting to create or reset it", "expected_result": "The system should prevent login and continue displaying the password complexity error message."}
    ]
  },
  {
    "scenario_name": "TC_032: Verify Account Lockout Duration",
    "type": "security",
    "prerequisites": "User account should be locked out due to three failed login attempts.",
    "Test Case Objective": "Verify that the account lockout mechanism enforces a specific duration before the account can be accessed again.",
    "steps": [
      {"action": "Verify if user is able to attempt to log in immediately after the account lockout", "expected_result": "The system should continue to display the lockout message and prevent login."},
      {"action": "Verify if user is able to attempt to log in after the specified lockout duration (e.g., 5 minutes)", "expected_result": "The system should allow the user to attempt to log in again without displaying the lockout message."},
      {"action": "Verify if user is able to successfully log in with valid credentials after the lockout duration has expired", "expected_result": "User dashboard should be displayed upon successful login with valid credentials."}
    ]
  },
  {
    "scenario_name": "TC_033: Verify Username Case Sensitivity",
    "type": "security",
    "prerequisites": "User should have a valid username and password combination.",
    "Test Case Objective": "Verify that the username field is case-insensitive during login.",
    "steps": [
      {"action": "Verify if user is able to enter the username with incorrect casing (e.g., if username is 'testUser', enter 'TestUser' or 'testuser')", "expected_result": "The system should either allow the login if the password is correct or display an invalid credentials message, treating the username as case-insensitive."},
      {"action": "Verify if user is able to enter a combination of upper and lowercase characters for username and the correct password", "expected_result": "User dashboard should be displayed upon successful login."},
      {"action": "Verify if user is able to create a new account with a username that differs only in casing from an existing username", "expected_result": "The system should prevent the creation of the new account and display an error message indicating that the username is already taken."}
    ]
  },
  {
    "scenario_name": "TC_034: Verify Login Attempts with Brute Force Attack Mitigation",
    "type": "security",
    "prerequisites": "User has a valid account and access to the login page.",
    "Test Case Objective": "Verify that the system mitigates brute-force attacks by implementing rate limiting on login attempts.",
    "steps": [
      {"action": "Verify if user is able to attempt a rapid series of incorrect login attempts (e.g., more than 5 attempts within 1 minute)", "expected_result": "The system should implement rate limiting and temporarily block the user's IP address or display a CAPTCHA challenge."},
      {"action": "Verify if user is able to attempt to log in from a different IP address after being rate-limited from the initial IP address", "expected_result": "The system should apply rate limiting rules separately for each unique IP address."},
      {"action": "Verify if user is able to successfully log in after the rate limiting period has expired", "expected_result": "User dashboard should be displayed upon successful login with valid credentials."}
    ]
  },
  {
    "scenario_name": "TC_035: Verify Password Reset Token Security",
    "type": "security",
    "prerequisites": "User has initiated a password reset request and received a password reset link via email.",
    "Test Case Objective": "Verify that the password reset token is unique, secure, and expires after a reasonable timeframe.",
    "steps": [
      {"action": "Verify if user is able to attempt to use the password reset link multiple times", "expected_result": "The system should invalidate the token after the first use and display an error message if the user attempts to use it again."},
      {"action": "Verify if user is able to attempt to use an expired password reset link", "expected_result": "The system should display an error message indicating that the token has expired and that the user needs to request a new password reset link."},
      {"action": "Verify if the password reset link contains a randomly generated, unpredictable token", "expected_result": "The token should be a long, random string and not contain easily guessable information such as the user's username or email address."}
    ]
  }
]
```