```json
[
  {
    "scenario_name": "TC_015_Lockout_After_Three_Failed_Attempts",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment, but also know the correct username and password.",
    "Test Case Objective": "Verify the account lockout mechanism is triggered after three consecutive failed login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to enter a valid username into the username field.",
        "expected_result": "The username should be accepted."
      },
      {
        "action": "Verify if user is able to enter an incorrect password into the password field.",
        "expected_result": "The password should be rejected, and an error message should be displayed."
      },
      {
        "action": "Verify if user is able to repeat the previous step two more times, entering an incorrect password.",
        "expected_result": "The system should display an error message indicating account lockout."
      },
      {
        "action": "Verify if user is able to attempt a fourth login with the correct credentials.",
        "expected_result": "The login attempt should fail and display an account lockout message."
      }
    ]
  },
  {
    "scenario_name": "TC_016_Successful_Login_After_Lockout",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment. Account should be locked from the previous test case.",
    "Test Case Objective": "Verify the system allows login after the lockout period has elapsed.",
    "steps": [
      {
        "action": "Verify if user is able to wait for the designated lockout duration.",
        "expected_result": "The account lockout message should remain, signifying that the account is locked."
      },
      {
        "action": "Verify if user is able to enter a valid username into the username field.",
        "expected_result": "The username should be accepted."
      },
       {
        "action": "Verify if user is able to enter the correct password into the password field.",
        "expected_result": "The password should be accepted."
      },
      {
        "action": "Verify if user is able to click the login button after the lockout time has passed.",
        "expected_result": "The system should successfully authenticate the user and redirect to the home page."
      }
    ]
  }
]
```