[{"scenario_name": "InvalidCredentialsLockout", "type": "negative", "prerequisites": "User should have access to the login page.", "Test Case Objective": "Verify that the system correctly locks the account after three unsuccessful login attempts with invalid credentials.", "steps": [{"action": "Verify if user is able to login with an incorrect username and a correct password.", "expected_result": "An error message indicating incorrect credentials should be displayed."}, {"action": "Verify if user is able to login with a correct username and an incorrect password.", "expected_result": "An error message indicating incorrect credentials should be displayed."}, {"action": "Verify if user is able to login with completely incorrect username and password.", "expected_result": "An error message indicating incorrect credentials should be displayed."}, {"action": "Verify if user is able to login again with the correct credentials after three failed attempts.", "expected_result": "An account locked message should be displayed."}]}, {"scenario_name": "ExcessiveLoginAttempts", "type": "negative", "prerequisites": "User should have access to the login page and attempt to login with invalid credentials repeatedly.", "Test Case Objective": "Verify the system's response to multiple consecutive failed login attempts exceeding the lockout threshold.", "steps": [{"action": "Verify if user is able to enter a username and leave the password field blank, then attempt to login.", "expected_result": "An error message prompting the user to enter a password should be displayed."}, {"action": "Verify if user is able to enter random characters in the username field and random characters in the password field, and then attempt to login multiple times until account lockout.", "expected_result": "An account locked message should be displayed after exceeding three failed attempts."}, {"action": "Verify if user is able to use SQL Injection attempts in both username and password fields and then attempt to login.", "expected_result": "An error message indicating invalid input and preventing login should be displayed, and no account compromise should occur."}, {"action": "Verify if user is able to repeatedly submit the login form with intentional delays exceeding a reasonable time-out period.", "expected_result": "The system should still enforce the three-attempt lockout, not allowing login until an appropriate unlock period has passed."}]}]