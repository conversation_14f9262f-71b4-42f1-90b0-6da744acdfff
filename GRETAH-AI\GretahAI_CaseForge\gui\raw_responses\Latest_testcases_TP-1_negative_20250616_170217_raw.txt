[
  {
    "scenario_name": "InvalidUsername_SpecialChars",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system rejects login attempts with usernames containing only special characters.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username containing only special characters (!@#$%^&*()_+=-`~[]\{}|;':\",./<>?) in the username field.",
        "expected_result": "An error message indicating an invalid username format should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with this invalid username and a valid password.",
        "expected_result": "The login attempt should fail, and the same error message should persist."
      },
      {
        "action": "Verify if user is able to see any unexpected behavior or error messages beyond the expected error message.",
        "expected_result": "No unexpected behavior or error messages should be observed."
      }
    ]
  },
  {
    "scenario_name": "InvalidPassword_TooShort",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system rejects login attempts with passwords shorter than the minimum allowed length.",
    "steps": [
      {
        "action": "Verify if user is able to enter a password shorter than the minimum required length in the password field.",
        "expected_result": "An error message indicating that the password is too short should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with a valid username and this too-short password.",
        "expected_result": "The login attempt should fail, and the error message should persist."
      },
      {
        "action": "Verify if user is able to try again with different passwords of similar insufficient length.",
        "expected_result": "The same error message indicating password length should be displayed for each attempt."
      }
    ]
  },
  {
    "scenario_name": "EmptyFieldsSubmission",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system handles empty username and password fields appropriately.",
    "steps": [
      {
        "action": "Verify if user is able to submit the login form without entering a username.",
        "expected_result": "An error message indicating that the username field is required should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form without entering a password.",
        "expected_result": "An error message indicating that the password field is required should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with both username and password fields left empty.",
        "expected_result": "Error messages should be displayed indicating that both fields are required."
      }
    ]
  },
  {
    "scenario_name": "AccountLockout_MultipleInvalidAttempts",
    "type": "negative",
    "prerequisites": "User should have a valid account and access to the login page.",
    "Test Case Objective": "Verify that the system correctly locks an account after three consecutive failed login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to enter an incorrect password three times consecutively.",
        "expected_result": "The account should be locked after the third failed attempt."
      },
      {
        "action": "Verify if user is able to see a clear message indicating that their account is locked.",
        "expected_result": "A clear message stating that the account is locked should be displayed."
      },
      {
        "action": "Verify if user is able to attempt login again immediately after the account is locked.",
        "expected_result": "The login attempt should fail, and the account locked message should be redisplayed."
      },
      {
        "action": "Verify if user is able to access any features or content that require login.",
        "expected_result": "Access should be denied."
      }
    ]
  },
  {
    "scenario_name": "XSS_UsernameInjection",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system prevents Cross-Site Scripting (XSS) attacks through username field.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username containing a script tag (e.g., <script>alert('XSS')</script>).",
        "expected_result": "The system should prevent the execution of the script and not display the alert."
      },
      {
        "action": "Verify if user is able to submit the login form with this malicious username and a valid password.",
        "expected_result": "The login attempt should fail and display an error message related to invalid username format, not executing the script."
      },
      {
        "action": "Verify if any unexpected behavior or error messages are displayed beyond the expected error message.",
        "expected_result": "No unexpected behavior or error messages should be observed."
      },
      {
        "action": "Verify if user is able to observe any indication that the script attempted to execute.",
        "expected_result": "There should be no evidence of script execution."
      }
    ]
  }
]
