```json
[
  {
    "scenario_name": "Successful_Login_with_Valid_Credentials",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment and access to the login page.",
    "Test Case Objective": "Verify successful login using valid credentials and access to the application's home page.",
    "steps": [
      {
        "action": "Verify if user is able to enter a valid username into the username field",
        "expected_result": "The username should be accepted and displayed correctly."
      },
      {
        "action": "Verify if user is able to enter a valid password into the password field",
        "expected_result": "The password should be accepted and displayed correctly."
      },
      {
        "action": "Verify if user is able to click the login button",
        "expected_result": "The system should successfully authenticate the user and redirect to the application's home page."
      },
      {
        "action": "Verify if user is able to view the application's home page",
        "expected_result": "The application's home page should be displayed without any errors."
      }
    ]
  },
  {
    "scenario_name": "Account_Lockout_After_Three_Failed_Logins",
    "type": "positive",
    "prerequisites": "User should have an account in the test environment.",
    "Test Case Objective": "Verify that the system locks out an account after three consecutive failed login attempts with invalid credentials.",
    "steps": [
      {
        "action": "Verify if user is able to enter an invalid username into the username field",
        "expected_result": "The username should be rejected."
      },
      {
        "action": "Verify if user is able to enter an invalid password into the password field",
        "expected_result": "The password should be rejected and an error message displayed."
      },
      {
        "action": "Verify if user is able to repeat steps 1 and 2 two more times with invalid credentials",
        "expected_result": "The system should display a message indicating account lockout after three failed attempts."
      },
      {
        "action": "Verify if user is able to attempt another login after a specified lockout period.",
        "expected_result": "The user should be able to log in successfully after the lockout period has expired."
      }
    ]
  }
]
```