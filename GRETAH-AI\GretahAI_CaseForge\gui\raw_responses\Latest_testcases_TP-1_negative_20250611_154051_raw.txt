```json
[
  {
    "scenario_name": "Invalid_Username_Format",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system rejects login attempts with usernames containing only special characters.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username containing only special characters (e.g., '!@#$%^&*') in the username field.",
        "expected_result": "An error message indicating an invalid username format should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with the invalid username and a valid password.",
        "expected_result": "The login attempt should fail, and the error message should persist."
      },
      {
        "action": "Verify if user is able to attempt login again with the same invalid username and a valid password.",
        "expected_result": "The same error message indicating an invalid username format should be displayed."
      }
    ]
  },
  {
    "scenario_name": "Password_Too_Short",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system rejects login attempts with passwords shorter than the minimum allowed length.",
    "steps": [
      {
        "action": "Verify if user is able to enter a password shorter than the minimum allowed length in the password field.",
        "expected_result": "An error message indicating that the password is too short should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with a valid username and the too-short password.",
        "expected_result": "The login attempt should fail."
      },
      {
        "action": "Verify if user is able to see an error message indicating the minimum password length requirement.",
        "expected_result": "An error message specifying the minimum password length should be displayed."
      }
    ]
  },
  {
    "scenario_name": "Empty_Password_Field",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system rejects login attempts with an empty password field.",
    "steps": [
      {
        "action": "Verify if user is able to leave the password field empty and submit the login form.",
        "expected_result": "An error message indicating that the password field is required should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with a valid username and an empty password field.",
        "expected_result": "The login attempt should fail."
      },
      {
        "action": "Verify if user is able to see an error message indicating that the password field cannot be empty.",
        "expectedResult": "An appropriate error message should be displayed."
      }
    ]
  },
  {
    "scenario_name": "Excessive_Login_Attempts",
    "type": "negative",
    "prerequisites": "User should have access to the login page and valid (but incorrect) credentials.",
    "Test Case Objective": "Verify that the system locks the account after three unsuccessful login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to enter incorrect credentials three times consecutively.",
        "expected_result": "The account should be locked after the third failed attempt."
      },
      {
        "action": "Verify if user is able to see a clear message indicating their account is locked.",
        "expected_result": "A message clearly stating the account is locked should be displayed."
      },
      {
        "action": "Verify if user is able to attempt login again immediately after the account lock.",
        "expected_result": "The login attempt should fail, and the account locked message should be redisplayed."
      }
    ]
  },
  {
    "scenario_name": "SQL_Injection_Attempt",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system prevents SQL injection attempts in the username field.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username containing SQL injection code (e.g., ' OR '1'='1) in the username field.",
        "expected_result": "The system should prevent the injection attempt and not allow login."
      },
      {
        "action": "Verify if user is able to submit the login form with the SQL injection attempt and a valid password.",
        "expected_result": "The login attempt should fail."
      },
      {
        "action": "Verify if user is able to see an appropriate error message upon detecting a potential SQL injection attempt.",
        "expected_result": "A generic error message, or no message at all (preventing information leakage), should be displayed."
      },
      {
        "action": "Verify if user is able to access any unauthorized data or functionality after attempting SQL injection.",
        "expected_result": "User should not gain access to unauthorized data or functionality."
      }
    ]
  }
]
```
