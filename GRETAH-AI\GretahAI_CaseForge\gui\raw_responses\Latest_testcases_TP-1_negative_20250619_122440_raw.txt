```json
[
  {
    "scenario_name": "InvalidUsername_SpecialChars",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system rejects login attempts with usernames containing invalid special characters.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username with only special characters (e.g., !@#$%^&*) and a valid password.",
        "expected_result": "An error message indicating invalid username format should be displayed."
      },
      {
        "action": "Verify if user is able to enter a username with leading/trailing spaces and a valid password.",
        "expected_result": "An error message indicating invalid username format should be displayed."
      },
      {
        "action": "Verify if user is able to enter a username exceeding the maximum allowed length and a valid password.",
        "expected_result": "An error message indicating username length exceeded should be displayed."
      }
    ]
  },
  {
    "scenario_name": "InvalidPassword_Length",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system rejects login attempts with passwords that do not meet length requirements.",
    "steps": [
      {
        "action": "Verify if user is able to enter a valid username and a password shorter than the minimum allowed length.",
        "expected_result": "An error message indicating password too short should be displayed."
      },
      {
        "action": "Verify if user is able to enter a valid username and a password longer than the maximum allowed length.",
        "expected_result": "An error message indicating password too long should be displayed."
      },
      {
        "action": "Verify if user is able to enter a valid username and an empty password field.",
        "expected_result": "An error message indicating that the password field is required should be displayed."
      }
    ]
  },
  {
    "scenario_name": "MultipleFailedLoginAttempts",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system locks the account after three unsuccessful login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to enter an invalid username and password three times consecutively.",
        "expected_result": "An account lockout message should be displayed after the third failed attempt."
      },
      {
        "action": "Verify if user is able to see a countdown timer or a message indicating the time until the account is unlocked.",
        "expected_result": "A timer or message indicating the lockout duration should be displayed."
      },
      {
        "action": "Verify if user is able to login with valid credentials after the lockout period has elapsed.",
        "expected_result": "User should be successfully logged in after the lockout period."
      }
    ]
  },
  {
    "scenario_name": "SQLInjectionAttempt",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system prevents SQL injection attempts during login.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username containing SQL injection characters (e.g., ';', '--', 'drop table') and a valid password.",
        "expected_result": "The system should not allow the login and display an error message related to invalid input or security."
      },
      {
        "action": "Verify if user is able to enter a password containing SQL injection characters and a valid username.",
        "expected_result": "The system should not allow the login and display an error message related to invalid input or security."
      },
      {
        "action": "Verify if the application logs the attempted SQL injection.",
        "expected_result": "The attempted SQL injection should be logged in the system's security logs."
      }
    ]
  },
  {
    "scenario_name": "EmptyCredentials",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system handles empty username and password fields appropriately.",
    "steps": [
      {
        "action": "Verify if user is able to submit the login form with an empty username field and a valid password.",
        "expected_result": "An error message indicating a required username should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with a valid username and an empty password field.",
        "expected_result": "An error message indicating a required password should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with both username and password fields empty.",
        "expected_result": "Error messages indicating required username and password should be displayed."
      },
      {
        "action": "Verify if error messages are displayed in a user-friendly manner.",
        "expected_result": "Error messages should be clear, concise, and easy to understand."
      }
    ]
  }
]
```
