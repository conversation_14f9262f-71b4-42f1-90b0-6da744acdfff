```json
[
  {
    "scenario_name": "HighConcurrencyLogin",
    "type": "performance",
    "prerequisites": "User should have access to multiple test accounts with valid credentials for the test environment.",
    "Test Case Objective": "Verify the system's response time and resource utilization under high concurrent login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to simulate 100 concurrent login attempts with valid credentials.",
        "expected_result": "The average response time for successful logins should be under 2 seconds."
      },
      {
        "action": "Verify if user is able to monitor CPU and memory usage during the test.",
        "expected_result": "CPU usage should remain below 80%, and memory usage should remain below 90%."
      },
      {
        "action": "Verify if user is able to observe any error messages or exceptions during the test.",
        "expected_result": "No errors or exceptions should be reported in system logs."
      }
    ]
  },
  {
    "scenario_name": "StressTestLoginFailures",
    "type": "performance",
    "prerequisites": "User should have access to a single test account with valid credentials for the test environment.",
    "Test Case Objective": "Verify the system's stability and responsiveness under a large number of consecutive failed login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to attempt 500 consecutive login attempts with invalid credentials for a single account.",
        "expected_result": "The system should remain responsive without crashing or experiencing significant performance degradation."
      },
      {
        "action": "Verify if user is able to monitor system resource usage (CPU, memory, network) during the test.",
        "expected_result": "Resource utilization should remain within acceptable limits (e.g., CPU < 80%, Memory < 90%, Network < 70%)."
      },
      {
        "action": "Verify if user is able to confirm that the account is locked after three consecutive failed attempts.",
        "expected_result": "The account should be locked after three incorrect password attempts, and a lockout message should be displayed."
      }
    ]
  },
  {
    "scenario_name": "LoadTestPasswordReset",
    "type": "performance",
    "prerequisites": "User should have access to multiple test accounts with valid credentials.  User needs to be able to trigger password resets.",
    "Test Case Objective": "Verify the system's performance under a high volume of concurrent password reset requests.",
    "steps": [
      {
        "action": "Verify if user is able to simulate 50 concurrent password reset requests.",
        "expected_result": "The average response time for each password reset request should be under 5 seconds."
      },
      {
        "action": "Verify if user is able to monitor email delivery time during the test.",
        "expected_result": "All password reset emails should be delivered within 1 minute."
      },
      {
        "action": "Verify if user is able to check for any errors in the system logs during the test.",
        "expected_result": "No errors related to password reset requests should be logged."
      }
    ]
  },
  {
    "scenario_name": "ResourceUsageHighLoginVolume",
    "type": "performance",
    "prerequisites": "User should have access to a large number of test accounts with valid credentials and sufficient system monitoring tools.",
    "Test Case Objective": "Verify system resource consumption (CPU, memory, disk I/O, network) during a high volume of successful logins.",
    "steps": [
      {
        "action": "Verify if user is able to execute 200 successful login attempts within a 5-minute interval.",
        "expected_result": "All login attempts should succeed within the expected time limit."
      },
      {
        "action": "Verify if user is able to monitor CPU and Memory usage.",
        "expected_result": "CPU usage should not exceed 75%, and memory usage should not exceed 85% during the test."
      },
      {
        "action": "Verify if user is able to monitor Disk I/O and Network usage.",
        "expected_result": "Disk I/O and network usage should remain within acceptable thresholds."
      },
      {
        "action": "Verify if user is able to check for any errors or exceptions recorded during the test.",
        "expected_result": "The system logs should not contain any errors or exceptions."
      }
    ]
  },
  {
    "scenario_name": "SustainedLoadLogins",
    "type": "performance",
    "prerequisites": "User should have access to multiple test accounts with valid credentials and tools to maintain a sustained load.",
    "Test Case Objective": "Verify system performance under a sustained load of 50 concurrent login attempts for 30 minutes.",
    "steps": [
      {
        "action": "Verify if user is able to maintain 50 concurrent login attempts for 30 minutes using a load testing tool.",
        "expected_result": "The system should maintain responsiveness and stability throughout the test duration."
      },
      {
        "action": "Verify if user is able to monitor response times for login requests during the test.",
        "expected_result": "Average response time should remain below 3 seconds during the entire test duration."
      },
      {
        "action": "Verify if user is able to monitor system resource utilization (CPU, memory, network) over the 30-minute period.",
        "expected_result": "Resource utilization should remain stable and within acceptable limits (e.g., CPU < 80%, Memory < 90%, Network < 70%)."
      },
      {
        "action": "Verify if user is able to review system logs for any errors or exceptions that occurred during the test.",
        "expected_result": "No significant errors or performance-related exceptions should be logged."
      }
    ]
  }
]
```
