```json
[
  {
    "scenario_name": "InvalidEmailFormat",
    "type": "negative",
    "prerequisites": "User should have access to the login page and the 'Forgot Password' functionality.",
    "Test Case Objective": "Verify that the system handles invalid email formats during password reset.",
    "steps": [
      {
        "action": "Verify if user is able to enter an email address with an invalid format (e.g., missing '@' symbol) and submit the request.",
        "expected_result": "An error message indicating an invalid email format should be displayed."
      },
      {
        "action": "Verify if user is able to enter an email address with extra characters (e.g., '<EMAIL>!!!') and submit the request.",
        "expected_result": "An error message indicating an invalid email format should be displayed."
      },
      {
        "action": "Verify if user is able to leave the email field blank and submit the request.",
        "expected_result": "An error message indicating that the email field is required should be displayed."
      }
    ]
  },
  {
    "scenario_name": "ExpiredResetLink",
    "type": "negative",
    "prerequisites": "User should have received a password reset email and waited for more than 30 minutes.",
    "Test Case Objective": "Verify that an expired password reset link displays an appropriate error message.",
    "steps": [
      {
        "action": "Verify if user is able to wait more than 30 minutes after receiving the password reset email.",
        "expected_result": "Sufficient time should pass."
      },
      {
        "action": "Verify if user is able to click the expired password reset link in the email.",
        "expected_result": "An error message indicating that the password reset link has expired should be displayed."
      },
      {
        "action": "Verify if user is able to attempt to reset the password using the expired link.",
        "expected_result": "The password reset should fail, displaying an error message related to link expiry."
      }
    ]
  },
  {
    "scenario_name": "WeakPassword",
    "type": "negative",
    "prerequisites": "User should have a valid password reset link and access to the password reset page.",
    "Test Case Objective": "Verify that the system rejects passwords that do not meet the complexity requirements.",
    "steps": [
      {
        "action": "Verify if user is able to enter a password that is shorter than the minimum length requirement and submit.",
        "expected_result": "An error message indicating the password is too short should be displayed."
      },
      {
        "action": "Verify if user is able to enter a password that only contains numbers and submit.",
        "expected_result": "An error message indicating the password does not meet complexity requirements should be displayed."
      },
      {
        "action": "Verify if user is able to enter a password that contains only lowercase letters and submit.",
        "expected_result": "An error message indicating that the password must contain uppercase letters should be displayed."
      }
    ]
  },
  {
    "scenario_name": "NonMatchingPasswords",
    "type": "negative",
    "prerequisites": "User should have a valid password reset link and access to the password reset page.",
    "Test Case Objective": "Verify that the system prevents password resets if the new password and confirmation do not match.",
    "steps": [
      {
        "action": "Verify if user is able to enter a new password and a mismatched confirmation password.",
        "expected_result": "An error message indicating that the passwords do not match should be displayed."
      },
      {
        "action": "Verify if user is able to leave the password confirmation field blank and submit.",
        "expected_result": "An error message indicating that the password confirmation field is required should be displayed."
      }
    ]
  },
  {
    "scenario_name": "NonExistentEmail",
    "type": "negative",
    "prerequisites": "User should have access to the login page and the 'Forgot Password' functionality.",
    "Test Case Objective": "Verify that the system handles requests for password resets with non-existent email addresses.",
    "steps": [
      {
        "action": "Verify if user is able to enter a non-existent email address and submit the request.",
        "expected_result": "An appropriate message should be displayed indicating that the email address is not found or registered."
      },
      {
        "action": "Verify if user is able to enter an email address with typos and submit the request.",
        "expected_result": "An appropriate message should be displayed indicating that the email address is not found or registered."
      },
      {
        "action": "Verify if user is able to enter an email address with special characters and submit the request.",
        "expected_result": "An appropriate message should be displayed indicating that the email address is not found or registered."
      },
      {
        "action": "Verify if user is able to leave the email field blank and submit the request.",
        "expected_result": "An error message indicating that the email field is required should be displayed."
      }
    ]
  }
]
```
