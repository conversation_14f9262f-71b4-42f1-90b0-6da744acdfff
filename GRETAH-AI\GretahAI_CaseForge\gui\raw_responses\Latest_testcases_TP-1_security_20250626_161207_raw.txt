```json
[
  {
    "scenario_name": "Invalid_Login_Attempts",
    "type": "security",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify the lockout functionality after three incorrect login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to enter incorrect username in the User ID field and an incorrect password in the password field",
        "expected_result": "Incorrect username and password combination should be rejected"
      },
      {
        "action": "Verify if user is able to repeat step 1 two more times, entering incorrect credentials each time",
        "expected_result": "Incorrect username and password combination should be rejected each attempt"
      },
      {
        "action": "Verify if user is able to access the application after entering the correct username and password",
        "expected_result": "User should be successfully logged in"
      },
      {
        "action": "Verify if user is able to attempt to log in with the same incorrect credentials again immediately after the successful log in.",
        "expected_result": "The user should be blocked or presented with an appropriate error message indicating they have exceeded the maximum login attempts."
      }
    ]
  },
  {
    "scenario_name": "Input_Sanitization_Login",
    "type": "security",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the application does not allow special characters or SQL injection attempts in the login fields.",
    "steps": [
      {
        "action": "Verify if user is able to enter special characters (e.g., < > & \" ; ) in the username field",
        "expected_result": "Special characters should be blocked or sanitized and not accepted"
      },
      {
        "action": "Verify if user is able to enter SQL injection attempts (e.g., ' or \" or -- or -- in the username or password field",
        "expected_result": "SQL injection attempts should be rejected and not accepted."
      },
      {
        "action": "Verify if user is able to enter a long string as username greater than 200 characters",
        "expected_result": "The application should not allow input longer than the specified limit for usernames."
      },
      {
        "action": "Verify if user is able to enter a password including special character in the password field and enter the correct username.",
        "expected_result": "The special character in the password should be blocked or sanitized, while a valid username should successfully proceed with login."
      }
    ]
  }
]
```