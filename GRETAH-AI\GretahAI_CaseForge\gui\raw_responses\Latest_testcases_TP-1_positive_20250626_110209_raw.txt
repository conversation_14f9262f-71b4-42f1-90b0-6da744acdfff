```json
[
  {
    "scenario_name": "Successful_Login_with_valid_credentials",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify successful login with valid username and password, and access to the home page.",
    "steps": [
      {
        "action": "Verify if user is able to navigate to the login page.",
        "expected_result": "Login page should be displayed."
      },
      {
        "action": "Verify if user is able to enter a valid username into the username field.",
        "expected_result": "Username should be accepted."
      },
      {
        "action": "Verify if user is able to enter a valid password into the password field.",
        "expected_result": "Password should be accepted."
      },
      {
        "action": "Verify if user is able to click the login button.",
        "expected_result": "The system should successfully authenticate the user."
      },
      {
        "action": "Verify if user is able to access the application's home page.",
        "expected_result": "The application's home page should be displayed."
      }
    ]
  },
  {
    "scenario_name": "Account_Lockout_after_failed_attempts",
    "type": "positive",
    "prerequisites": "User should have an account in the system and valid credentials.",
    "Test Case Objective": "Verify the account lockout mechanism after three consecutive failed login attempts with invalid passwords.",
    "steps": [
      {
        "action": "Verify if user is able to enter a valid username.",
        "expected_result": "Username should be accepted."
      },
      {
        "action": "Verify if user is able to enter an incorrect password three consecutive times.",
        "expected_result": "The system should display an error message after each incorrect attempt."
      },
      {
        "action": "Verify if user is able to attempt a fourth login after the lockout period.",
        "expected_result": "The system should allow login after the lockout period has elapsed."
      },
      {
          "action": "Verify if user is able to access the application after the lockout period.",
          "expected_result": "The application's home page should be displayed."
      }
    ]
  }
]
```