```json
[
  {
    "scenario_name": "Login_HighConcurrency",
    "type": "performance",
    "prerequisites": "User should have access to the login page with valid test credentials.",
    "Test Case Objective": "Verify the system's ability to handle a high volume of concurrent login requests without performance degradation.",
    "steps": [
      {"action": "Verify if user is able to simulate 100 concurrent login requests using valid credentials.", "expected_result": "All 100 login requests should be processed successfully within an average response time of 2 seconds."},
      {"action": "Verify if user is able to measure the CPU and memory usage on the server during the simulated login load.", "expected_result": "The CPU usage should not exceed 80% and memory usage should not exceed 70% during the load test."},
      {"action": "Verify if user is able to check for any errors or exceptions logged during the concurrent login test.", "expected_result": "No critical errors or exceptions related to authentication should be logged during the test."}
    ]
  },
  {
    "scenario_name": "Login_FailedAttemptsRateLimit",
    "type": "performance",
    "prerequisites": "User should have access to the login page and a test account with rate limiting enabled.",
    "Test Case Objective": "Verify the system's rate limiting functionality under repeated failed login attempts and measure the lockout duration.",
    "steps": [
      {"action": "Verify if user is able to enter incorrect credentials repeatedly (e.g., 5 times in 1 minute) to trigger the rate limit.", "expected_result": "The system should trigger rate limiting and display a message indicating too many failed attempts."},
      {"action": "Verify if user is able to attempt to login again immediately after receiving the rate limit message.", "expected_result": "The system should continue to block login attempts for a predefined duration (e.g., 5 minutes)."},
      {"action": "Verify if user is able to attempt to login after the predefined lockout duration has elapsed.", "expected_result": "The system should allow login attempts again after the lockout duration and reset the failed attempts counter."},
      {"action": "Verify if user is able to measure the average response time for failed login attempts before and after rate limiting is triggered.", "expected_result": "The response time for failed login attempts should remain consistent or slightly increase after rate limiting is activated."}
    ]
  },
  {
    "scenario_name": "Login_AccountLockoutStressTest",
    "type": "performance",
    "prerequisites": "User should have access to the login page and a test account with account lockout enabled after 3 failed attempts.",
    "Test Case Objective": "Verify the system's ability to handle account lockout under stress, ensuring no service disruption during peak activity.",
    "steps": [
      {"action": "Verify if user is able to simulate 50 users repeatedly failing their login attempts (3 times each) to trigger account lockouts simultaneously.", "expected_result": "The system should successfully lock out all 50 accounts without causing a service disruption or crash."},
      {"action": "Verify if user is able to monitor the database connection pool during the simultaneous account lockouts.", "expected_result": "The database connection pool should not be exhausted, and connections should be released promptly."},
      {"action": "Verify if user is able to check the error logs for any database errors or exceptions during the stress test.", "expected_result": "No database errors or exceptions related to account lockout should be logged."},
      {"action": "Verify if user is able to measure the time taken to unlock a sample of these accounts after the lockout duration.", "expected_result": "Accounts should be unlocked automatically within the expected timeframe after the lockout duration (e.g., 5 minutes)."}
    ]
  },
  {
    "scenario_name": "Login_BruteForceAttackDetection",
    "type": "performance",
    "prerequisites": "User should have a valid account and access to a tool that can simulate a brute-force attack on the login endpoint.",
    "Test Case Objective": "Verify the system's detection and mitigation of a brute-force login attack by monitoring system resource usage and response times.",
    "steps": [
      {"action": "Verify if user is able to simulate a brute-force attack with a large number of login attempts (e.g., 1000 attempts with random credentials) within a short period (e.g., 1 minute).", "expected_result": "The system should detect the attack and implement rate limiting or block the attacking IP address."},
      {"action": "Verify if user is able to monitor the server's CPU and memory usage during the simulated brute-force attack.", "expected_result": "The CPU and memory usage should increase but remain within acceptable limits (e.g., below 90%) without causing service degradation."},
      {"action": "Verify if user is able to analyze the server logs for detected brute-force attempts and mitigation actions.", "expected_result": "The server logs should contain entries indicating the detection of the brute-force attack and the implemented mitigation measures (e.g., IP blocking)."},
       {"action": "Verify if user is able to measure the average response time for legitimate login attempts during the simulated brute-force attack.", "expected_result": "The average response time for legitimate login attempts should not be significantly impacted during the attack due to the implemented mitigation measures."}
    ]
  },
  {
    "scenario_name": "Login_SessionExpiryPerformance",
    "type": "performance",
    "prerequisites": "User should have a valid account and the system configured with a defined session expiry time.",
    "Test Case Objective": "Verify session expiry functionality and its impact on system resources under a simulated user load.",
    "steps": [
      {"action": "Verify if user is able to simulate 50 concurrent users logging in and remaining idle for a period exceeding the session expiry time (e.g., 30 minutes).", "expected_result": "All 50 user sessions should be automatically terminated after the session expiry time."},
      {"action": "Verify if user is able to monitor the number of active sessions on the server before and after the session expiry time.", "expected_result": "The number of active sessions should decrease to zero or a very low number after the session expiry time."},
      {"action": "Verify if user is able to measure the memory usage on the server before and after the session expiry time.", "expected_result": "Memory usage associated with user sessions should be released after session expiry, resulting in a decrease in overall memory usage."},
      {"action": "Verify if user is able to attempt to access a protected resource after the session expiry time using one of the simulated users.", "expected_result": "The user should be redirected to the login page, indicating that their session has expired."}
    ]
  }
]
```