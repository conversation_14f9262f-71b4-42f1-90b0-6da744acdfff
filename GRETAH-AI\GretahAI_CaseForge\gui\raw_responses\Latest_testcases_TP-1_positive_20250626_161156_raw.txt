```json
[
  {
    "scenario_name": "Successful Login",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify successful login process with valid credentials.",
    "steps": [
      {
        "action": "Verify if user is able to enter valid username in the User ID field",
        "expected_result": "Username should be accepted and displayed correctly."
      },
      {
        "action": "Verify if user is able to enter valid password in the Password field",
        "expected_result": "Password should be accepted and displayed."
      },
      {
        "action": "Verify if user is able to click the Login button",
        "expected_result": "User should be successfully logged in and redirected to the home page."
      },
       {
        "action": "Verify if user is able to view the welcome message",
        "expected_result": "A welcome message should be displayed on the home page."
      }
    ]
  },
  {
    "scenario_name": "Login with Correct Credentials",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify that the application accepts correct username and password combination and logs in successfully.",
    "steps": [
      {
        "action": "Verify if user is able to enter correct username in the User ID field",
        "expected_result": "Correct username should be accepted."
      },
      {
        "action": "Verify if user is able to enter correct password in the Password field",
        "expected_result": "Correct password should be accepted."
      },
       {
        "action": "Verify if user is able to click the Login button",
        "expected_result": "User should be successfully logged in to the application."
      },
       {
        "action": "Verify if user is able to navigate to other application pages",
        "expected_result": "User should be able to navigate to other pages of the application."
      }
    ]
  }
]
```
