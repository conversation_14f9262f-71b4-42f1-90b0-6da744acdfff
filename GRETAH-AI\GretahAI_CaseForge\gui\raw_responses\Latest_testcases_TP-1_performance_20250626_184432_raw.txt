```json
[
  {
    "scenario_name": "ConcurrentLoginStress",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment and access to the login page.",
    "Test Case Objective": "Verify the system's ability to handle a high volume of concurrent login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to submit 100 concurrent login requests with valid credentials.",
        "expected_result": "All 100 login requests should be processed within a response time under 5 seconds."
      },
      {
        "action": "Verify if user is able to submit 200 concurrent login requests with invalid credentials.",
        "expected_result": "The system should reject the 200 requests within a response time under 10 seconds."
      },
      {
        "action": "Verify if user is able to monitor CPU usage during concurrent login attempts.",
        "expected_result": "CPU usage should remain below 80%."
      },
      {
        "action": "Verify if user is able to monitor memory usage during concurrent login attempts.",
        "expected_result": "Memory usage should remain below 90%."
      }
    ]
  },
  {
    "scenario_name": "LoginFailureRate",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment and access to the login page.",
    "Test Case Objective": "Verify system performance under a high rate of login failures.",
    "steps": [
      {
        "action": "Verify if user is able to submit 1000 invalid login attempts in a short time frame.",
        "expected_result": "The system should reject all attempts within a response time under 20 seconds, and the login rate should be 15 attempts per second maximum."
      },
      {
        "action": "Verify if user is able to monitor the server logs for error messages related to failed login attempts.",
        "expected_result": "Server logs should contain the expected error messages without system crashes."
      },
      {
        "action": "Verify if user is able to measure the response time of subsequent valid login attempts after a high rate of invalid attempts.",
        "expected_result": "Subsequent valid login attempts should have a consistent response time (under 1.5 seconds). "
      }
    ]
  },
  {
    "scenario_name": "LoginConcurrency",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment and access to the login page.",
    "Test Case Objective": "Verify the system's ability to handle concurrent logins with varying user loads.",
    "steps": [
      {
        "action": "Verify if user is able to submit 50 concurrent login requests with valid credentials.",
        "expected_result": "All 50 login requests should be processed within a response time under 2 seconds."
      },
      {
        "action": "Verify if user is able to gradually increase the number of concurrent login requests to 100 and then 200, assessing the response time at each stage.",
        "expected_result": "Response time should remain stable with increasing concurrent requests (under 3 seconds). "
      },
      {
        "action": "Verify if user is able to monitor application latency.",
        "expected_result": "Application latency should be under 1ms."
      }
    ]
  },
  {
    "scenario_name": "LoginStressTest",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment and access to the login page.",
    "Test Case Objective": "Validate system performance under sustained load through repeated login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to submit 1000 consecutive valid login attempts.",
        "expected_result": "All 1000 login attempts should complete successfully within a response time under 30 seconds."
      },
      {
        "action": "Verify if user is able to repeat the process to test for server instability.",
        "expected_result": "The system should maintain its stability and response time without any noticeable latency."
      },
       {
        "action": "Verify if user is able to monitor resource usage during sustained login load.",
        "expected_result": "System resources (CPU, memory, network) should remain within acceptable thresholds during the load."
      }
    ]
  },
{
    "scenario_name": "LoginLockoutPerformance",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment and access to the login page.",
    "Test Case Objective": "Verify system performance after a user is locked out.",
    "steps": [
      {
        "action": "Verify if user is able to submit 3 incorrect login attempts.",
        "expected_result": "The system should prevent further attempts for 1 minute."
      },
      {
        "action": "Verify if user is able to access the login page after the lockout period.",
        "expected_result": "User should be able to login successfully."
      },
      {
        "action": "Verify if user is able to submit multiple valid login attempts after the lockout period.",
        "expected_result": "All attempts should be processed successfully within a reasonable time."
      }
    ]
  }
]
```