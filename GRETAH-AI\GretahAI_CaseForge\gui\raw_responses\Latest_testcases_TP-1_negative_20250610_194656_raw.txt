[{"scenario_name": "Invalid_Username_Format", "type": "negative", "prerequisites": "User should have access to the login page.", "Test Case Objective": "Verify that the system rejects login attempts with usernames containing only special characters.", "steps": [{"action": "Verify if user is able to enter a username containing only special characters (e.g., '!@#$%^&*') in the username field.", "expected_result": "An error message indicating an invalid username format should be displayed."}, {"action": "Verify if user is able to submit the login form with the invalid username and a valid password.", "expected_result": "The login attempt should fail, and the error message should persist."}, {"action": "Verify if user is able to attempt login again with the same invalid username and a valid password.", "expected_result": "The same error message indicating an invalid username format should be displayed."}]}, {"scenario_name": "Account_Lockout_Edge_Case", "type": "negative", "prerequisites": "User should have access to the login page and an account with valid credentials.", "Test Case Objective": "Verify that the system correctly handles account lockout when multiple users attempt invalid logins concurrently.", "steps": [{"action": "Verify if user is able to simulate 2 users concurrently each attempting 3 invalid logins.", "expected_result": "Both accounts should be locked after 3 failed attempts each."}, {"action": "Verify if user is able to observe any errors or unexpected behavior in the system during the concurrent login attempts.", "expected_result": "No errors or unexpected behavior should occur; the system should correctly lock both accounts individually."}, {"action": "Verify if user is able to attempt login again immediately after lockout with one of the locked accounts.", "expected_result": "The login attempt should fail and the account locked message should be displayed."}, {"action": "Verify if user is able to login after unlocking one of the accounts through the password reset workflow.", "expected_result": "User should be able to successfully login using correct credentials."}]}]