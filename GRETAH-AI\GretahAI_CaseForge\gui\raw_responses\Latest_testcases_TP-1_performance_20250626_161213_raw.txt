```json
[
  {
    "scenario_name": "Login_Stress_Test_1",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify the system's response time under concurrent login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to initiate 50 concurrent login attempts with valid credentials.",
        "expected_result": "All 50 login requests should be initiated successfully within 2 seconds."
      },
      {
        "action": "Verify if user is able to measure the average response time for successful logins for the 50 concurrent attempts.",
        "expected_result": "The average response time for successful logins should be less than 1 second."
      },
      {
        "action": "Verify if user is able to monitor CPU usage during the 50 concurrent login attempts.",
        "expected_result": "CPU usage should not exceed 80%."
      },
      {
        "action": "Verify if user is able to monitor memory usage during the 50 concurrent login attempts.",
        "expected_result": "Memory usage should not exceed 75%."
      }
    ]
  },
  {
    "scenario_name": "High_Concurrency_Login",
    "type": "performance",
    "prerequisites": "User should have access to run load testing tools and monitor system resources.",
    "Test Case Objective": "Verify the system's ability to handle a high volume of concurrent login requests.",
    "steps": [
      {
        "action": "Verify if user is able to simulate 100 concurrent login requests with valid credentials.",
        "expected_result": "All 100 login requests should be initiated successfully within 5 seconds."
      },
      {
        "action": "Verify if user is able to measure the average response time for successful logins during the 100 concurrent attempts.",
        "expected_result": "The average response time for successful logins should be less than 1.5 seconds."
      },
      {
        "action": "Verify if user is able to monitor the number of failed login attempts within 30 minutes.",
        "expected_result": "The number of failed login attempts should be zero."
      },
       {
        "action": "Verify if user is able to check the server load during the high concurrency login attempts.",
        "expected_result": "Server load should remain below a 70% threshold."
      }
    ]
  }
]
```