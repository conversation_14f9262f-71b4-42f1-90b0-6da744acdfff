[{"scenario_name": "InvalidEmailFormat", "type": "negative", "prerequisites": "User should have access to the login page.", "Test Case Objective": "Verify that the system handles invalid email formats during password reset.", "steps": [{"action": "Verify if user is able to click the 'Forgot Password' link.", "expected_result": "The password reset form should be displayed."}, {"action": "Verify if user is able to enter an email address with an invalid format (e.g., missing @ symbol).", "expected_result": "An appropriate error message indicating invalid email format should be displayed."}, {"action": "Verify if user is able to submit the form with an invalid email.", "expected_result": "The form should not be submitted, and the error message should persist."}, {"action": "Verify if user is able to enter an email address with special characters before the @ symbol.", "expected_result": "An appropriate error message should be displayed."}]}, {"scenario_name": "ExpiredResetLink", "type": "negative", "prerequisites": "User should have received a password reset link via email.", "Test Case Objective": "Verify that the system handles expired password reset links.", "steps": [{"action": "Verify if user is able to wait for more than 30 minutes after receiving the reset link.", "expected_result": "The time should be tracked."}, {"action": "Verify if user is able to click on the password reset link after the 30-minute expiration.", "expected_result": "An error message indicating that the link has expired should be displayed."}, {"action": "Verify if user is able to attempt to reset the password using the expired link.", "expected_result": "The password reset should fail, and the error message should be displayed."}]}, {"scenario_name": "WeakPassword", "type": "negative", "prerequisites": "User should have a valid password reset link and access to the password reset form.", "Test Case Objective": "Verify that the system enforces password complexity requirements.", "steps": [{"action": "Verify if user is able to enter a password that does not meet the minimum length requirement.", "expected_result": "An error message indicating password length requirement should be displayed."}, {"action": "Verify if user is able to enter a password that lacks required character types (e.g., uppercase, numbers, symbols).", "expected_result": "An error message indicating missing character type(s) should be displayed."}, {"action": "Verify if user is able to enter a password that is too similar to the old password.", "expected_result": "An appropriate error message should be displayed."}]}, {"scenario_name": "NonExistentEmail", "type": "negative", "prerequisites": "User should have access to the login page.", "Test Case Objective": "Verify that the system handles requests for password resets with non-existent email addresses.", "steps": [{"action": "Verify if user is able to click the 'Forgot Password' link.", "expected_result": "The password reset form should be displayed."}, {"action": "Verify if user is able to enter an email address that is not registered in the system.", "expected_result": "An error message indicating that the email address is not found should be displayed."}, {"action": "Verify if user is able to submit the form with a non-existent email address.", "expected_result": "The system should not send a password reset email."}]}, {"scenario_name": "MultipleRequests", "type": "negative", "prerequisites": "User should have access to the login page.", "Test Case Objective": "Verify the system's response to multiple password reset requests within a short timeframe.", "steps": [{"action": "Verify if user is able to request a password reset multiple times within a short period (e.g., 5 times in 1 minute).", "expected_result": "The system should handle requests appropriately (e.g., display a message indicating too many requests or temporarily block the email address)."}, {"action": "Verify if user is able to receive multiple password reset emails after submitting multiple requests.", "expected_result": "The system should only send one password reset email or display a message of handling multiple requests."}, {"action": "Verify if user is able to access the password reset link from any of the multiple emails sent.", "expected_result": "Only one link should be functional, or an error message should be displayed, indicating that a password reset has already been initiated."}]}]