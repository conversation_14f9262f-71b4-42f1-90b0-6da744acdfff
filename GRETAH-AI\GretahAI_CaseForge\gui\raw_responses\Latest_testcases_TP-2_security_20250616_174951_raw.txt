[{"scenario_name": "Password Reset Link Expiration", "type": "security", "prerequisites": "User should have an account in the test environment and have initiated a password reset.", "Test Case Objective": "Verify that a password reset link expires after 30 minutes and prevents unauthorized access.", "steps": [{"action": "Verify if user is able to access the password reset link immediately after it has been sent.", "expected_result": "Password reset link should be accessible and allow the user to proceed."}, {"action": "Verify if user is able to access the password reset link after 30 minutes.", "expected_result": "Password reset link should be expired and prevent access."}, {"action": "Verify if user is able to reset the password using an expired link.", "expected_result": "System should display an error message indicating that the link has expired."}, {"action": "Verify if an error message is displayed when attempting to use the expired link indicating the link is expired and preventing any further attempts.", "expected_result": "Appropriate error message should be displayed, clearly indicating the link is invalid and expired."}]}, {"scenario_name": "Password Reset Input Sanitization", "type": "security", "prerequisites": "User should have an account in the test environment and have initiated a password reset.", "Test Case Objective": "Verify that the password reset functionality properly sanitizes user inputs to prevent injection attacks.", "steps": [{"action": "Verify if user is able to submit the password reset form with SQL injection attempts in the email field.", "expected_result": "System should prevent submission and display an appropriate error message."}, {"action": "Verify if user is able to submit the password reset form with cross-site scripting (XSS) attempts in the email field.", "expected_result": "System should prevent submission and sanitize the input, preventing XSS attacks."}, {"action": "Verify if user is able to submit the password reset form with special characters and unusual input sequences in the new password field.", "expected_result": "System should accept the input (if valid in length and format) and not exhibit unexpected behavior."}]}]