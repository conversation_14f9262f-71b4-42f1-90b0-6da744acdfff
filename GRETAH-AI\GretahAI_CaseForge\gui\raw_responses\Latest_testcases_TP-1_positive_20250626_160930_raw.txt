```json
[
  {
    "scenario_name": "SuccessfulLoginAfterLockout",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment and the account should be locked out.",
    "Test Case Objective": "Verify that a user can successfully log in after the lockout period has expired.",
    "steps": [
      {
        "action": "Verify if user is able to wait for the specified lockout duration to pass.",
        "expected_result": "The lockout timer should expire after the specified duration."
      },
      {
        "action": "Verify if user is able to enter valid credentials for the locked account.",
        "expected_result": "The user should be able to enter a valid username and password without error."
      },
      {
        "action": "Verify if user is able to click the Login button.",
        "expected_result": "User should be logged in successfully and redirected to the user dashboard."
      },
      {
        "action": "Verify if user is able to access a protected resource on the user dashboard.",
        "expected_result": "The user should be able to access the dashboard without any issues."
      }
    ]
  },
  {
    "scenario_name": "LockoutAfterThreeFailedAttempts",
    "type": "positive",
    "prerequisites": "User should have an account with valid credentials for the test environment.",
    "Test Case Objective": "Verify that the system locks a user out after three failed login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to enter incorrect credentials for the login.",
        "expected_result": "Login attempt should fail and display an 'invalid credentials' error."
      },
      {
        "action": "Verify if user is able to enter incorrect credentials for a second login attempt.",
        "expected_result": "Login attempt should fail and display an 'invalid credentials' error."
      },
      {
        "action": "Verify if user is able to enter incorrect credentials for a third login attempt.",
        "expected_result": "The system should lockout the account and display a clear lockout message."
      },
      {
        "action": "Verify if user is able to attempt further login attempts immediately after the lockout.",
        "expected_result": "Further login attempts should be rejected due to account lockout."
      }
    ]
  }
]
```