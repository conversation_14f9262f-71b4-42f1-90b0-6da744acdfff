```json
[
  {
    "scenario_name": "Session_Hijacking_Attempt",
    "type": "security",
    "prerequisites": "User should have access to the application and a valid account.  User should also have the ability to monitor network traffic.",
    "Test Case Objective": "Verify that the application protects against session hijacking attempts.",
    "steps": [
      {
        "action": "Verify if user is able to successfully log in to the application.",
        "expected_result": "The application's home page should be displayed."
      },
      {
        "action": "Verify if user is able to capture their session ID from the network traffic.",
        "expected_result": "The session ID should be captured successfully."
      },
      {
        "action": "Verify if user is able to use the captured session ID in a separate browser or session to access the application.",
        "expected_result": "Access should be denied, and the user should be redirected to the login page."
      },
      {
        "action": "Verify if the application logs the unauthorized session access attempt.",
        "expected_result": "The unauthorized access attempt should be logged in the application's security logs."
      }
    ]
  },
  {
    "scenario_name": "Cross_Site_Scripting_Prevention",
    "type": "security",
    "prerequisites": "User should have a valid account and permissions to access the application's input fields.",
    "Test Case Objective": "Verify that the application prevents Cross-Site Scripting (XSS) attacks.",
    "steps": [
      {
        "action": "Verify if user is able to enter a malicious script (e.g., <script>alert('XSS')</script>) into a text input field.",
        "expected_result": "The script should not execute, and the input should be sanitized."
      },
      {
        "action": "Verify if user is able to observe any unexpected behavior after submitting the input containing the malicious script.",
        "expected_result": "No unexpected behavior, such as pop-up alerts or page redirection, should occur."
      },
      {
        "action": "Verify if the application's source code reveals the malicious script after submission.",
        "expected_result": "The malicious script should not be present in the application's rendered source code."
      }
    ]
  },
  {
    "scenario_name": "SQL_Injection_Attempt_Password_Field",
    "type": "security",
    "prerequisites": "User should have access to the application login page.",
    "Test Case Objective": "Verify that the application prevents SQL injection attacks in the password field.",
    "steps": [
      {
        "action": "Verify if user is able to enter SQL injection code (e.g., 'OR '1'='1') into the password field.",
        "expected_result": "The login attempt should fail."
      },
      {
        "action": "Verify if user is able to observe any database errors or unusual behavior.",
        "expected_result": "No database errors or unexpected behavior should be observed."
      },
      {
        "action": "Verify if user is able to gain unauthorized access to the application.",
        "expected_result": "Unauthorized access should be denied."
      }
    ]
  },
  {
    "scenario_name": "Data_Confidentiality_during_Password_Reset",
    "type": "security",
    "prerequisites": "User should have a registered account and access to the password reset functionality.",
    "Test Case Objective": "Verify that the application protects the confidentiality of user data during password reset.",
    "steps": [
      {
        "action": "Verify if user is able to initiate a password reset request.",
        "expected_result": "A password reset email should be sent to the registered email address."
      },
      {
        "action": "Verify if the password reset email contains sensitive information such as the user's password in plain text.",
        "expected_result": "The email should not contain the user's password in plain text; instead, it should contain a secure link or token."
      },
      {
        "action": "Verify if the link or token used for password reset expires after a reasonable time frame.",
        "expected_result": "The link or token should expire and render unusable after a pre-defined time."
      },
      {
        "action": "Verify if user is able to intercept and use the password reset link or token.",
        "expected_result": "Using an intercepted link or token should fail to provide access if appropriate security measures are in place."
      }
    ]
  },
  {
    "scenario_name": "Authorization_Check_Protected_Resource",
    "type": "security",
    "prerequisites": "User should have access to the application and different user roles with varying permissions.",
    "Test Case Objective": "Verify that the application enforces proper authorization for accessing protected resources.",
    "steps": [
      {
        "action": "Verify if user is able to log in with a user account that lacks authorization for a specific resource.",
        "expected_result": "Access to the protected resource should be denied."
      },
      {
        "action": "Verify if user is able to bypass authorization controls to access the restricted resource (e.g., by manipulating URLs).",
        "expected_result": "Access to the resource should still be denied."
      },
      {
        "action": "Verify if user is able to see an appropriate error message or access denial message when attempting to access a resource without authorization.",
        "expected_result": "A clear and informative message indicating insufficient permissions should be shown."
      }
    ]
  }
]
```
