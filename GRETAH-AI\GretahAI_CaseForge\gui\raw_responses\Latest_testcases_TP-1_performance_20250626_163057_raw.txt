```json
[
  {
    "scenario_name": "Login_Load_Test",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify the system's performance under a heavy load of concurrent login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to simultaneously initiate 100 login attempts with valid credentials.",
        "expected_result": "All 100 login attempts should complete successfully within 5 seconds."
      },
      {
        "action": "Verify if user is able to simultaneously initiate 200 login attempts with invalid credentials.",
        "expected_result": "The system should respond to each attempt within 10 seconds, and the lockout mechanism should be triggered for invalid attempts."
      },
      {
        "action": "Verify if user is able to monitor CPU utilization and memory consumption during the load test.",
        "expected_result": "CPU and memory utilization should not exceed 80%."
      },
      {
        "action": "Verify if user is able to analyze the response time of the login API endpoint during the high load.",
        "expected_result": "The average response time for successful login attempts should remain below 100ms."
      }
    ]
  },
  {
    "scenario_name": "Concurrent_Login_Stress",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify the system's ability to handle a high volume of concurrent login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to simultaneously generate 500 concurrent login attempts with valid credentials.",
        "expected_result": "The system should not crash or experience significant performance degradation."
      },
      {
        "action": "Verify if user is able to monitor system response time during the high concurrency period.",
        "expected_result": "Response time should remain below 150ms for most of the concurrent login attempts."
      },
      {
        "action": "Verify if user is able to monitor the system logs for any errors during the stress test.",
        "expected_result": "No critical errors should be logged."
      }
    ]
  },
  {
    "scenario_name": "High_Concurrency_Login_Test",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify the response time and stability of the system during high concurrency.",
    "steps": [
        {
            "action": "Verify if user is able to simulate 1000 concurrent users attempting to login with valid credentials.",
            "expected_result": "The system should maintain an average response time of less than 200ms."
        },
        {
            "action": "Verify if user is able to assess system resource utilization (CPU, memory, network) during peak concurrency.",
            "expected_result": "System resource utilization should not exceed 90%."
        },
        {
            "action": "Verify if user is able to observe the effectiveness of the system's load balancing mechanism under high concurrency.",
            "expected_result": "The load balancing mechanism should distribute the load evenly across available servers, ensuring a consistent response time."
        }
    ]
  },
    {
    "scenario_name": "Login_API_Performance",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment and access to the login API.",
    "Test Case Objective": "Verify the performance of the login API endpoint under various loads.",
    "steps": [
      {
        "action": "Verify if user is able to initiate a series of API calls to simulate a moderate load on the login API.",
        "expected_result": "API response time should remain below 150ms."
      },
      {
        "action": "Verify if user is able to stress test the login API with high volume of concurrent requests.",
        "expected_result": "The API should handle 500 concurrent requests without significant performance degradation."
      }
    ]
  },
  {
    "scenario_name": "Database_Load_Test_Login",
    "type": "performance",
    "prerequisites": "User should have access to tools to monitor database performance.",
    "Test Case Objective": "Verify the database performance under a load test while simulating login activities.",
    "steps": [
        {
            "action": "Verify if user is able to execute a load test simulating 200 users logging in simultaneously.",
            "expected_result": "Database response time should remain under 50ms during the load test."
        },
        {
            "action": "Verify if user is able to analyze the database query performance during the login attempt.",
            "expected_result": "The average query response time should be below 20ms."
        }
    ]
  }
]
```