[{"scenario_name": "InvalidUsername_SpecialChars", "type": "negative", "prerequisites": "User should have access to the login page.", "Test Case Objective": "Verify that the system rejects login attempts with usernames containing only special characters.", "steps": [{"action": "Verify if user is able to enter a username containing only special characters (e.g., '!@#$%^&*') in the username field.", "expected_result": "An error message indicating an invalid username format should be displayed."}, {"action": "Verify if user is able to submit the login form with only special characters in the username field.", "expected_result": "The login attempt should fail."}, {"action": "Verify if user is able to see a specific error message indicating invalid username format.", "expected_result": "An error message explicitly stating 'Invalid username format' or similar should be displayed."}]}, {"scenario_name": "Password_TooShort", "type": "negative", "prerequisites": "User should have access to the login page.", "Test Case Objective": "Verify that the system rejects login attempts with passwords shorter than the minimum allowed length.", "steps": [{"action": "Verify if user is able to enter a password shorter than the minimum allowed length in the password field.", "expected_result": "An error message indicating password length is too short should be displayed."}, {"action": "Verify if user is able to submit the login form with a password shorter than the minimum length.", "expected_result": "The login attempt should fail."}, {"action": "Verify if user is able to see an error message specifying the minimum password length.", "expected_result": "An error message should clearly state the minimum password length requirement (e.g., 'Password must be at least 8 characters')."}]}, {"scenario_name": "EmptyPassword_Submission", "type": "negative", "prerequisites": "User should have access to the login page and a valid username.", "Test Case Objective": "Verify that the system rejects login attempts with an empty password field.", "steps": [{"action": "Verify if user is able to enter a valid username in the username field.", "expected_result": "The username should be accepted."}, {"action": "Verify if user is able to leave the password field empty and submit the login form.", "expected_result": "An error message indicating that the password field is required should be displayed."}, {"action": "Verify if user is able to submit the form again without entering a password.", "expected_result": "The same error message should be displayed."}]}, {"scenario_name": "Login_After_Lockout", "type": "negative", "prerequisites": "User should have access to the login page and an account.", "Test Case Objective": "Verify that the system prevents login attempts immediately after account lockout.", "steps": [{"action": "Verify if user is able to enter incorrect credentials three times consecutively.", "expected_result": "The account should be locked after the third failed attempt."}, {"action": "Verify if user is able to attempt login immediately after the account is locked.", "expected_result": "The login attempt should fail."}, {"action": "Verify if user is able to see a clear message indicating their account is locked.", "expected_result": "A clear message stating that the account is locked should be displayed."}, {"action": "Verify if user is able to attempt login again immediately after seeing the lockout message.", "expected_result": "The login attempt should still fail and display the same lockout message."}]}, {"scenario_name": "Invalid_Email_Password_Reset", "type": "negative", "prerequisites": "User should have access to the login page.", "Test Case Objective": "Verify that the system handles invalid email addresses during password reset requests.", "steps": [{"action": "Verify if user is able to locate the 'Forgot Password' link or button on the login page.", "expected_result": "'Forgot Password' link or button should be visible."}, {"action": "Verify if user is able to enter an invalid email address (e.g., missing '@' symbol) in the password reset form.", "expected_result": "An error message indicating an invalid email address format should be displayed."}, {"action": "Verify if user is able to submit the password reset request with an invalid email address.", "expected_result": "The request should fail, and the error message should persist."}, {"action": "Verify if user is able to see a specific error message indicating invalid email format.", "expected_result": "An error message explicitly mentioning 'Invalid email format' or a similar message should be displayed."}]}]