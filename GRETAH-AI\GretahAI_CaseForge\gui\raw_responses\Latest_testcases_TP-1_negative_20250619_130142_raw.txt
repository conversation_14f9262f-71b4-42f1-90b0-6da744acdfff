[{"scenario_name": "Invalid_Username_Format", "type": "negative", "prerequisites": "User should have access to the login page.", "Test Case Objective": "Verify that the system rejects login attempts with usernames containing invalid characters.", "steps": [{"action": "Verify if user is able to enter a username with only numbers (e.g., 1234567890) and a valid password.", "expected_result": "An error message indicating invalid username format should be displayed."}, {"action": "Verify if user is able to enter a username with special characters at the beginning and end (e.g., @username@) and a valid password.", "expected_result": "An error message indicating invalid username format should be displayed."}, {"action": "Verify if user is able to enter a username containing spaces in the middle (e.g., user name) and a valid password.", "expected_result": "An error message indicating invalid username format should be displayed."}]}, {"scenario_name": "Password_Security_Check", "type": "negative", "prerequisites": "User should have access to the login page and valid username.", "Test Case Objective": "Verify that the system prevents login with a password containing only numbers or special characters.", "steps": [{"action": "Verify if user is able to enter a valid username and a password containing only numbers (e.g., 12345678).", "expected_result": "An error message indicating that the password must meet complexity requirements should be displayed."}, {"action": "Verify if user is able to enter a valid username and a password containing only special characters (e.g., !@#$%^&*).", "expected_result": "An error message indicating that the password must meet complexity requirements should be displayed."}, {"action": "Verify if user is able to enter a valid username and a password shorter than the minimum required length.", "expected_result": "An error message indicating password is too short should be displayed."}, {"action": "Verify if user is able to enter a valid username and a password that is longer than the maximum allowed length.", "expected_result": "An error message indicating password is too long should be displayed."}]}]