```json
[
  {
    "scenario_name": "SQLInjectionAttempt",
    "type": "security",
    "prerequisites": "User should have access to the login page and attempt to exploit vulnerabilities.",
    "Test Case Objective": "Verify that the system is protected against SQL injection attacks during login.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username containing SQL injection characters (e.g., ';--') into the username field.",
        "expected_result": "The system should prevent the submission and display an error message indicating invalid input."
      },
      {
        "action": "Verify if user is able to enter a password containing SQL injection characters (e.g., 'OR 1=1--') into the password field.",
        "expected_result": "The system should prevent the submission and display an error message indicating invalid input."
      },
      {
        "action": "Verify if user is able to combine both username and password fields with SQL injection attempts.",
        "expected_result": "The system should prevent the submission and display an error message indicating invalid input."
      }
    ]
  },
  {
    "scenario_name": "SessionHijackingAttempt",
    "type": "security",
    "prerequisites": "User should have successfully logged in to the application.  User should have the ability to monitor network traffic (e.g., using browser developer tools).",
    "Test Case Objective": "Verify the system's protection against session hijacking attempts.",
    "steps": [
      {
        "action": "Verify if user is able to identify and capture their session ID from browser cookies or network traffic after successful login.",
        "expected_result": "The session ID should be obtained."
      },
      {
        "action": "Verify if user is able to use a separate browser or account to access the application using the captured session ID.",
        "expected_result": "The system should prevent unauthorized access using the captured session ID."
      },
      {
        "action": "Verify if user is able to change the session ID to a random value and attempt to access the application.",
        "expected_result": "The system should prevent unauthorized access using the altered session ID."
      },
      {
        "action": "Verify if user is able to close the original browser session and then use the previously captured session ID to access the application.",
        "expected_result": "The system should prevent unauthorized access using the closed session ID."
      }
    ]
  }
]
```
