```json
[
  {
    "scenario_name": "AccountLockoutAfterThreeFailedAttempts",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify the system locks out a user after three failed login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to enter a valid username into the username field.",
        "expected_result": "Username should be accepted."
      },
      {
        "action": "Verify if user is able to enter an incorrect password into the password field.",
        "expected_result": "Password should be rejected, and an error message should be displayed."
      },
      {
        "action": "Verify if user is able to repeat steps 2 two more times, entering incorrect passwords.",
        "expected_result": "The system should display an error message confirming the account lockout."
      },
      {
        "action": "Verify if user is able to attempt to log in again after a specified time (e.g., 15 minutes).",
        "expected_result": "The system should allow login after the lockout period has elapsed"
      }
    ]
  },
  {
    "scenario_name": "SuccessfulLoginAfterLockout",
    "type": "positive",
    "prerequisites": "User account should be locked and the lockout period should have passed.",
    "Test Case Objective": "Verify the account lockout feature works, allowing login after the lockout period has expired.",
    "steps": [
      {
        "action": "Verify if user is able to enter the valid username.",
        "expected_result": "Username should be accepted."
      },
	  {
        "action": "Verify if user is able to enter the correct password.",
        "expected_result": "Password should be accepted."
      },
      {
        "action": "Verify if user is able to click the login button.",
        "expected_result": "The user should be successfully logged in."
      },
      {
        "action": "Verify if user is able to access the application's home page.",
        "expected_result": "The application's home page should be displayed."
      }
    ]
  }
]
```