```json
[
  {
    "scenario_name": "ConcurrentLoginPerformance",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment and permission to access the login feature.",
    "Test Case Objective": "Verify the system's performance under a high load of concurrent login requests.",
    "steps": [
      {
        "action": "Verify if user is able to initiate 50 concurrent login requests with valid credentials",
        "expected_result": "All 50 login requests should be processed successfully."
      },
      {
        "action": "Verify if user is able to measure the average response time for each login request",
        "expected_result": "Average response time for login requests should be under 500ms"
      },
      {
        "action": "Verify if user is able to monitor system CPU utilization and memory usage during the concurrent login requests.",
        "expected_result": "CPU utilization should not exceed 80%, and memory usage should remain under 70%."
      },
      {
        "action": "Verify if user is able to log any errors during the concurrent login requests.",
        "expected_result": "System error logs should not indicate any critical errors during the test."
      }
    ]
  },
  {
    "scenario_name": "StressTestLockout",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment and permission to access the login feature.",
    "Test Case Objective": "Verify the system's ability to handle a significant number of failed login attempts without crashing or compromising response times",
    "steps": [
      {
        "action": "Verify if user is able to simulate 100 concurrent users repeatedly attempting invalid logins (each user attempting 3 incorrect logins within a short time period)",
        "expected_result": "All 100 users should experience account lockout according to the defined lockout parameters."
      },
      {
        "action": "Verify if user is able to monitor server CPU and memory resource usage while the simulated attacks are occurring.",
        "expected_result": "System resource utilization should not exceed 90% or cause significant performance degradation during account lockouts."
      },
      {
        "action": "Verify if user is able to measure the average response time for legitimate login attempts during the period of stress testing.",
        "expected_result": "Average response time for legitimate login attempts should remain consistent and within acceptable limits during the period of stress testing."
      },
      {
        "action": "Verify if user is able to observe whether the system automatically unlocks user accounts after the lockout duration.",
        "expected_result": "Locked accounts should be unlocked after the specified lockout duration, according to the system's settings."
      }
    ]
  }
]
```