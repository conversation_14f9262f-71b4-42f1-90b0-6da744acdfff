```json
[
  {
    "scenario_name": "SQLInjectionAttempt",
    "type": "security",
    "prerequisites": "User should have access to the login page and attempt to inject malicious SQL code.",
    "Test Case Objective": "Verify that the application prevents SQL injection attacks during login.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username containing SQL injection code (e.g., 'OR 1=1--') into the username field.",
        "expected_result": "The system should prevent the injection and display an error message or reject the input."
      },
      {
        "action": "Verify if user is able to enter a password containing SQL injection code (e.g., 'OR 1=1--') into the password field.",
        "expected_result": "The system should prevent the injection and display an error message or reject the input."
      },
      {
        "action": "Verify if user is able to submit the login form with SQL injection code in both username and password fields.",
        "expected_result": "The system should not allow login and should display an appropriate error message, indicating that the input is invalid."
      }
    ]
  },
  {
    "scenario_name": "CrossSiteScriptingAttempt",
    "type": "security",
    "prerequisites": "User should have access to the login page and attempt to inject malicious scripts.",
    "Test Case Objective": "Verify that the application prevents Cross-Site Scripting (XSS) attacks during login.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username containing a cross-site scripting payload (e.g., '<script>alert('XSS')</script>') into the username field.",
        "expected_result": "The system should properly sanitize the input and prevent the script from executing."
      },
      {
        "action": "Verify if user is able to enter a password containing a cross-site scripting payload (e.g., '<img src=\"x\" onerror=\"alert('XSS')\">') into the password field.",
        "expected_result": "The system should properly sanitize the input and prevent the script from executing."
      },
      {
        "action": "Verify if user is able to submit the login form with XSS payloads in both username and password fields.",
        "expected_result": "The system should not execute the malicious scripts and should display the input without executing the scripts."
      }
    ]
  },
  {
    "scenario_name": "SessionHijackingAttempt",
    "type": "security",
    "prerequisites": "User should have successfully logged in and their session ID should be accessible.",
    "Test Case Objective": "Verify that the application protects against session hijacking.",
    "steps": [
      {
        "action": "Verify if user is able to access the application using another browser or device with only the stolen session ID.",
        "expected_result": "The system should not allow access without proper authentication."
      },
      {
        "action": "Verify if user is able to modify the session ID and try to gain unauthorized access.",
        "expected_result": "The system should not allow access and should invalidate the modified session ID."
      }
    ]
  },
  {
    "scenario_name": "SensitiveDataExposure",
    "type": "security",
    "prerequisites": "User should have successfully logged in and access to features that handle sensitive data.",
    "Test Case Objective": "Verify that sensitive data is not exposed in the application's responses.",
    "steps": [
      {
        "action": "Verify if user is able to view any sensitive data (e.g., passwords, credit card numbers) in the application's source code or network traffic using browser developer tools.",
        "expected_result": "Sensitive data should not be directly visible in the application's source code or network traffic."
      },
      {
        "action": "Verify if user is able to intercept sensitive data transmitted between the application and the server using tools like Burp Suite.",
        "expected_result": "Sensitive data should be transmitted securely using encryption (HTTPS) and should not be accessible without proper authorization."
      }
    ]
  },
  {
    "scenario_name": "BruteForceProtection",
    "type": "security",
    "prerequisites": "User should have access to the login page and attempt to login with multiple invalid credentials.",
    "Test Case Objective": "Verify that the application effectively mitigates brute-force attacks.",
    "steps": [
      {
        "action": "Verify if user is able to enter three consecutive incorrect usernames and passwords.",
        "expected_result": "The system should lock the account after three failed attempts."
      },
      {
        "action": "Verify if user is able to see a clear message indicating account lockout after three failed attempts.",
        "expected_result": "A clear message should be displayed, informing the user of the lockout."
      },
      {
        "action": "Verify if user is able to unlock their account after waiting the specified lockout period.",
        "expected_result": "The system should allow the user to unlock the account after the lockout period."
      },
      {
        "action": "Verify if user is able to use password reset functionality to regain access after lockout.",
        "expected_result": "The system should provide a password reset mechanism to regain access."
      }
    ]
  }
]
```
