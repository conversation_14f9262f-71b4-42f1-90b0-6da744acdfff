[{"scenario_name": "Invalid_Username_Format", "type": "negative", "prerequisites": "User should have access to the login page.", "Test Case Objective": "Verify that the system rejects login attempts with usernames containing invalid characters.", "steps": [{"action": "Verify if user is able to enter a username containing special characters (!@#$%^&*).", "expected_result": "An error message indicating invalid username format should be displayed."}, {"action": "Verify if user is able to enter a username containing emojis.", "expected_result": "An error message indicating invalid username format should be displayed."}, {"action": "Verify if user is able to enter a username containing only spaces.", "expected_result": "An error message indicating invalid username format should be displayed."}, {"action": "Verify if user is able to enter a username exceeding the maximum allowed length.", "expected_result": "An error message indicating username length exceeded should be displayed."}]}, {"scenario_name": "Invalid_Password_Complexity", "type": "negative", "prerequisites": "User should have access to the login page and a registered account.", "Test Case Objective": "Verify that the system rejects login attempts with passwords that do not meet complexity requirements.", "steps": [{"action": "Verify if user is able to enter a password containing only numbers and is below minimum length.", "expected_result": "An error message indicating invalid password format and length should be displayed."}, {"action": "Verify if user is able to enter a password containing only special characters.", "expected_result": "An error message indicating invalid password format should be displayed."}, {"action": "Verify if user is able to enter a password that doesn't meet the complexity requirements (e.g., missing uppercase letter).", "expected_result": "An error message indicating the password does not meet the complexity requirements should be displayed."}, {"action": "Verify if user is able to enter a password with only spaces.", "expected_result": "An error message indicating invalid password should be displayed."}, {"action": "Verify if user is able to leave the password field empty and submit the login form.", "expected_result": "An error message indicating that the password field is required should be displayed."}]}]