```json
[
  {
    "scenario_name": "HighConcurrencyLogin",
    "type": "performance",
    "prerequisites": "User should have access to a load testing tool and the application's login endpoint.",
    "Test Case Objective": "Verify the system's response time and resource utilization under 1000 concurrent login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to initiate 1000 concurrent login attempts using a load testing tool.",
        "expected_result": "The average response time for all login requests should be less than 2 seconds."
      },
      {
        "action": "Verify if user is able to monitor the system's CPU utilization during the test.",
        "expected_result": "CPU utilization should remain below 80%."
      },
      {
        "action": "Verify if user is able to monitor the system's memory utilization during the test.",
        "expected_result": "Memory utilization should remain below 75%."
      }
    ]
  },
  {
    "scenario_name": "SustainedLoadLogin",
    "type": "performance",
    "prerequisites": "User should have access to a load testing tool and the application's login endpoint.",
    "Test Case Objective": "Verify system stability under sustained high load of 50 login attempts per second for 30 minutes.",
    "steps": [
      {
        "action": "Verify if user is able to sustain 50 login attempts per second for 30 minutes using a load testing tool.",
        "expected_result": "The system should remain responsive and stable throughout the test without exceeding 90% resource utilization."
      },
      {
        "action": "Verify if user is able to monitor the system's CPU utilization throughout the test.",
        "expected_result": "CPU utilization should remain below 85%."
      },
      {
        "action": "Verify if user is able to monitor the system's memory utilization throughout the test.",
        "expected_result": "Memory utilization should remain below 80%."
      },
      {
        "action": "Verify if user is able to check for any errors or exceptions logged during the test.",
        "expected_result": "No critical errors or exceptions should be logged."
      }
    ]
  },
  {
    "scenario_name": "FailedLoginStress",
    "type": "performance",
    "prerequisites": "User should have access to a load testing tool and the application's login endpoint.",
    "Test Case Objective": "Verify the system's performance when handling a large number of failed login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to simulate 500 consecutive failed login attempts using a load testing tool.",
        "expected_result": "The system should not crash."
      },
      {
        "action": "Verify if user is able to monitor the average response time for failed login requests.",
        "expected_result": "The average response time should not exceed 2 seconds."
      },
      {
        "action": "Verify if user is able to monitor CPU and memory usage during the test.",
        "expected_result": "Resource utilization should remain within acceptable limits (e.g., below 80%)."
      }
    ]
  },
  {
    "scenario_name": "LargeDatasetLogin",
    "type": "performance",
    "prerequisites": "User should have access to a test database with a large number of users (e.g., 100,000) and valid credentials for one of the users.",
    "Test Case Objective": "Verify the system's response time when handling login requests from a database with 100,000 users.",
    "steps": [
      {
        "action": "Verify if user is able to log in with a test account from a database containing 100,000 users.",
        "expected_result": "The login process should complete within 5 seconds."
      },
      {
        "action": "Verify if user is able to monitor the database query response times for the login request.",
        "expected_result": "The average database query response time should be below 200 milliseconds."
      },
      {
        "action": "Verify if user is able to monitor system resource utilization (CPU and memory) during the login process.",
        "expected_result": "Resource utilization should not exceed 80%."
      }
    ]
  },
  {
    "scenario_name": "ExtendedLoginSession",
    "type": "performance",
    "prerequisites": "User should have access to a load testing tool and the application's login endpoint.",
    "Test Case Objective": "Verify the system's ability to maintain performance over an extended period of continuous logins.",
    "steps": [
      {
        "action": "Verify if user is able to perform 1000 login attempts consecutively using a load testing tool.",
        "expected_result": "All login attempts should be processed successfully."
      },
      {
        "action": "Verify if user is able to monitor the average response time for all login requests throughout the test.",
        "expected_result": "The average response time should remain below 3 seconds."
      },
      {
        "action": "Verify if user is able to monitor the system's CPU and memory usage during the test.",
        "expected_result": "Resource utilization should remain within acceptable limits (e.g., below 80%)."
      },
      {
        "action": "Verify if user is able to check for any error messages or exceptions during the test.",
        "expected_result": "No critical errors or exceptions should be logged."
      }
    ]
  }
]
```
