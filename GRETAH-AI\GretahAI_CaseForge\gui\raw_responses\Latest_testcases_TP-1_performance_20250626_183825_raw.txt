```json
[
  {
    "scenario_name": "ConcurrentLoginStress",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify the system's ability to handle a high volume of concurrent login attempts without significant performance degradation.",
    "steps": [
      {
        "action": "Verify if user is able to initiate 100 concurrent login attempts simultaneously",
        "expected_result": "All 100 login attempts should be processed within 5 seconds."
      },
      {
        "action": "Verify if user is able to monitor the system resource utilization (CPU, Memory, Network) during the concurrent login attempts.",
        "expected_result": "System resource utilization should not exceed 80%."
      },
      {
        "action": "Verify if user is able to log successful login attempts within 200 milliseconds on average",
        "expected_result": "Successful login attempts should take less than 200 milliseconds on average."
      },
      {
        "action": "Verify if user is able to log 3 consecutive failed login attempts to trigger the lockout.",
        "expected_result": "The system should lock out the user account, presenting a lockout message, for the next 5 minutes"
      }
    ]
  },
  {
    "scenario_name": "LoginLoadTest",
    "type": "performance",
    "prerequisites": "User should have valid credentials for the test environment and be able to access the login page.",
    "Test Case Objective": "Verify the system's performance under a sustained load of login attempts.",
    "steps": [
      {
        "action": "Verify if user is able to simulate 500 concurrent users logging in over a period of 1 minute",
        "expected_result": "The system should maintain an average response time below 300 milliseconds."
      },
      {
        "action": "Verify if user is able to monitor the database transaction rate during the load test.",
        "expected_result": "Database transaction rate should be within acceptable limits (e.g., below 1000 transactions per second)."
      },
      {
        "action": "Verify if user is able to maintain the system's stability by logging failed login attempts and ensuring the system does not crash.",
        "expected_result": "The system should not crash or experience significant performance degradation during the load test."
      },
		{
        "action": "Verify if user is able to observe and measure the number of locked-out accounts in the database after the load test finishes.",
        "expected_result": "The number of locked-out accounts should match the expected number of lockout events (3 failed attempts per user)."
      }
    ]
  }
]
```