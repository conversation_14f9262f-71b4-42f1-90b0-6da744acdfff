```json
[
  {
    "scenario_name": "SQLInjectionAttempt_Username",
    "type": "security",
    "prerequisites": "User should have access to the login page and attempt to use SQL injection.",
    "Test Case Objective": "Verify that the system prevents SQL injection attempts in the username field.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username containing SQL injection code (e.g., ' OR '1'='1)",
        "expected_result": "The system should prevent the injection attempt and not allow login."
      },
      {
        "action": "Verify if user is able to observe any error messages related to SQL injection attempts.",
        "expected_result": "A generic error message, or no message at all (preventing information leakage), should be displayed."
      },
      {
        "action": "Verify if user is able to access any unauthorized data or functionality after attempting SQL injection.",
        "expected_result": "User should not gain access to unauthorized data or functionality."
      }
    ]
  },
  {
    "scenario_name": "SQLInjectionAttempt_Password",
    "type": "security",
    "prerequisites": "User should have access to the login page and attempt to use SQL injection.",
    "Test Case Objective": "Verify that the system prevents SQL injection attempts in the password field.",
    "steps": [
      {
        "action": "Verify if user is able to enter a password containing SQL injection code (e.g., ' OR '1'='1)",
        "expected_result": "The system should prevent the injection attempt and not allow login."
      },
      {
        "action": "Verify if user is able to observe any error messages related to SQL injection attempts.",
        "expected_result": "A generic error message, or no message at all (preventing information leakage), should be displayed."
      },
      {
        "action": "Verify if user is able to access any unauthorized data or functionality after attempting SQL injection.",
        "expected_result": "User should not gain access to unauthorized data or functionality."
      }
    ]
  },
  {
    "scenario_name": "SessionTimeout_Logout",
    "type": "security",
    "prerequisites": "User should be logged in to the system.",
    "Test Case Objective": "Verify that user sessions time out after a period of inactivity and require re-authentication.",
    "steps": [
      {
        "action": "Verify if user is able to remain inactive for a prolonged period (longer than the defined session timeout).",
        "expected_result": "The system should automatically log out the user after the defined inactivity period."
      },
      {
        "action": "Verify if user is able to access protected resources after the session timeout.",
        "expected_result": "User should be redirected to the login page and required to re-authenticate."
      },
      {
        "action": "Verify if user is able to see a clear message indicating that their session has timed out.",
        "expected_result": "A clear message should be displayed indicating that the session has timed out."
      }
    ]
  },
  {
    "scenario_name": "DataConfidentiality_PasswordReset",
    "type": "security",
    "prerequisites": "User should have a registered account and request a password reset.",
    "Test Case Objective": "Verify that password reset email does not reveal sensitive information.",
    "steps": [
      {
        "action": "Verify if user is able to request a password reset.",
        "expected_result": "A password reset email should be sent to the registered email address."
      },
      {
        "action": "Verify if user is able to examine the password reset email for sensitive information (e.g., user's full name, full password).",
        "expected_result": "The email should not contain any sensitive information beyond a secure link or code for password reset."
      },
      {
        "action": "Verify if user is able to reset their password using the provided link or code.",
        "expected_result": "User should be able to successfully reset their password."
      },
      {
        "action": "Verify if the password reset email is encrypted.",
        "expected_result": "The email content or link should use HTTPS or another appropriate encryption method."
      }
    ]
  },
  {
    "scenario_name": "Authorization_RoleBasedAccess",
    "type": "security",
    "prerequisites": "User should have different roles (e.g., Admin, User) with varying access permissions.",
    "Test Case Objective": "Verify that users with different roles have appropriate access levels.",
    "steps": [
      {
        "action": "Verify if user is able to access features and functionalities restricted to a higher role.",
        "expected_result": "User should be denied access if their role does not have the necessary permissions."
      },
      {
        "action": "Verify if user is able to perform actions only allowed for their specific role.",
        "expected_result": "User should be able to perform actions consistent with their assigned role's permissions."
      },
      {
        "action": "Verify if user is able to observe any error messages or notifications related to insufficient permissions.",
        "expected_result": "Appropriate messages should be displayed to the user indicating their lack of permissions."
      },
      {
        "action": "Verify if user is able to see a clear distinction between resources accessible with different roles.",
        "expected_result": "The system should effectively differentiate accessible resources based on user roles."
      }
    ]
  }
]
```
