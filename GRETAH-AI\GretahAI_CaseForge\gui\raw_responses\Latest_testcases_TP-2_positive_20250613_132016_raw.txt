[{"scenario_name": "Password Reset Email <PERSON>", "type": "positive", "prerequisites": "User should have a registered account with a valid email address.", "Test Case Objective": "Verify that a password reset email is sent to the registered email address upon request.", "steps": [{"action": "Verify if user is able to locate and click the \"Forgot Password\" link on the login page.", "expected_result": "The user should be redirected to a password reset request page."}, {"action": "Verify if user is able to enter their registered email address in the provided field.", "expected_result": "The system should accept the email address as input."}, {"action": "Verify if user is able to submit the password reset request.", "expected_result": "The system should send a password reset email to the entered email address."}, {"action": "Verify if user is able to check their inbox for the password reset email.", "expected_result": "An email containing a password reset link should be received."}]}, {"scenario_name": "Password Reset Link Functionality", "type": "positive", "prerequisites": "User should have received a valid password reset link via email.", "Test Case Objective": "Verify that clicking the password reset link redirects the user to the password change page.", "steps": [{"action": "Verify if user is able to access the password reset link from their email.", "expected_result": "The link should redirect the user to a password reset page."}, {"action": "Verify if user is able to see the password reset form fields.", "expected_result": "The page should display fields for new password and confirmation password."}]}, {"scenario_name": "New Password Set", "type": "positive", "prerequisites": "User should have accessed the password reset page via a valid link and entered a new password.", "Test Case Objective": "Verify that a user can successfully change their password after requesting a reset.", "steps": [{"action": "Verify if user is able to enter a new password that meets the complexity requirements.", "expected_result": "The system should accept the new password."}, {"action": "Verify if user is able to re-enter the new password in the confirmation field.", "expected_result": "The system should compare both password fields."}, {"action": "Verify if user is able to submit the new password.", "expected_result": "The system should update the user's password and redirect the user to the login page."}]}, {"scenario_name": "Successful Login After Password Reset", "type": "positive", "prerequisites": "User should have successfully reset their password.", "Test Case Objective": "Verify that the user can log in successfully after resetting their password.", "steps": [{"action": "Verify if user is able to navigate to the login page.", "expected_result": "The login page should be displayed."}, {"action": "Verify if user is able to enter their username and new password.", "expected_result": "The system should accept the credentials."}, {"action": "Verify if user is able to submit the login form.", "expected_result": "The user should be successfully logged in."}, {"action": "Verify if user is able to access their account functionality.", "expected_result": "The user should be able to access their account features."}]}, {"scenario_name": "Password Reset Link Expiration", "type": "positive", "prerequisites": "User should have received a password reset link and waited for it to expire.", "Test Case Objective": "Verify that a password reset link expires after 30 minutes.", "steps": [{"action": "Verify if user is able to obtain a password reset link via email.", "expected_result": "A password reset link should be sent to the user's email."}, {"action": "Verify if user waits for more than 30 minutes before attempting to use the link.", "expected_result": "The user should wait at least 31 minutes."}, {"action": "Verify if user is able to click the expired password reset link.", "expected_result": "The system should display a message indicating the link has expired."}, {"action": "Verify if user is able to request a new password reset link.", "expected_result": "The system should allow the user to request a new link."}]}]