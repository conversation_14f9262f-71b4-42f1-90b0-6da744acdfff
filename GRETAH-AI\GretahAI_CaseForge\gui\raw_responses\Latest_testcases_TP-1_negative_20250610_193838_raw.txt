[
  {
    "scenario_name": "Invalid_Username_Special_Chars",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify that the system rejects usernames containing excessive special characters.",
    "steps": [
      {
        "action": "Verify if user is able to enter a username containing only special characters (!@#$%^&*()_+=-`~[]\{}|;':\",./<>?).",
        "expected_result": "An error message indicating invalid username format should be displayed."
      },
      {
        "action": "Verify if user is able to enter a username exceeding the maximum length with special characters.",
        "expected_result": "An error message indicating username length exceeded should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with a username field containing only spaces and special characters.",
        "expected_result": "An error message indicating invalid username format should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with a username containing a mix of alphanumeric characters and an excessive number of special characters.",
        "expected_result": "An error message indicating invalid username format should be displayed."
      }
    ]
  },
  {
    "scenario_name": "Password_Edge_Cases",
    "type": "negative",
    "prerequisites": "User should have access to the login page.",
    "Test Case Objective": "Verify the system's rejection of passwords with various invalid formats and edge cases.",
    "steps": [
      {
        "action": "Verify if user is able to enter a password containing only whitespace characters.",
        "expected_result": "An error message should be displayed, indicating invalid password format."
      },
      {
        "action": "Verify if user is able to enter a password shorter than the minimum allowed length, using only numbers.",
        "expected_result": "An error message indicating password length is too short should be displayed."
      },
      {
        "action": "Verify if user is able to enter a password exceeding the maximum allowed length with only uppercase letters.",
        "expected_result": "An error message indicating password length exceeded should be displayed."
      },
      {
        "action": "Verify if user is able to submit the login form with a password field containing a mix of alphanumeric characters and an excessive number of special characters.",
        "expected_result": "An error message indicating invalid password format should be displayed."
      }
    ]
  }
]
