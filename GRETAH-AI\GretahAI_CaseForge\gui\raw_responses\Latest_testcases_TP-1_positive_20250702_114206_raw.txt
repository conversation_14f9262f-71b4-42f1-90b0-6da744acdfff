```json
[
  {
    "scenario_name": "Successful Login with Valid Credentials",
    "type": "positive",
    "prerequisites": "User should have valid credentials for the test environment.",
    "Test Case Objective": "Verify that a user is able to successfully log in to the system with valid credentials.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the username field", "expected_result": "The username should be entered successfully."},
      {"action": "Verify if user is able to enter valid password in the password field", "expected_result": "The password should be entered successfully."},
      {"action": "Verify if user is able to click on the 'Login' button", "expected_result": "The user should be redirected to the home page."},
      {"action": "Verify if user is able to view the home page", "expected_result": "The home page should be displayed with user's details."}
    ]
  },
  {
    "scenario_name": "Login with Correct Credentials After Lockout Reset",
    "type": "positive",
    "prerequisites": "User should have had their account locked due to previous failed login attempts, and the lockout timer should have expired or been manually reset by an administrator. User should also have valid login credentials.",
    "Test Case Objective": "Verify that a user, after having their account lockout reset, can successfully log in with correct credentials.",
    "steps": [
      {"action": "Verify if user is able to enter valid username in the username field", "expected_result": "The username should be entered successfully."},
      {"action": "Verify if user is able to enter valid password in the password field", "expected_result": "The password should be entered successfully."},
      {"action": "Verify if user is able to click the 'Login' button", "expected_result": "The system should authenticate the user."},
      {"action": "Verify if user is able to access the user's dashboard", "expected_result": "The user's dashboard should be displayed."},
      {"action": "Verify if user is able to navigate the application normally", "expected_result": "The user should be able to navigate to other sections of the application without any errors."}
    ]
  }
]
```